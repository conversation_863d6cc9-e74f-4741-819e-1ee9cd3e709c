# Trello Color Scheme Implementation

This document describes the comprehensive Trello-like color scheme implementation in the application, featuring official Trello colors, dynamic backgrounds, and accessibility-compliant theming.

## 🎨 Official Trello Color Palette

The implementation uses the official Trello brand colors:

### Primary Colors
- **<PERSON><PERSON> (Trello Blue)**: `#0079bf` - Primary brand color
- **<PERSON><PERSON>e (Green)**: `#70b500` - Success states
- **Tree Poppy (Orange)**: `#ff9f1a` - Warning states
- **<PERSON>t <PERSON> (Red)**: `#eb5a46` - Error states
- **School Bus Yellow**: `#f2d600` - Warning/attention states

### Secondary Colors
- **Lavender (Purple)**: `#c377e0` - Accent color
- **Hot Pink**: `#ff78cb` - Accent color
- **Robin's Egg Blue**: `#00c2e0` - Info states
- **Shamrock (Light Green)**: `#51e898` - Success accent
- **Silver Sand (Grey)**: `#c4c9cc` - Neutral states

## 🌈 Dynamic Board Backgrounds

### Solid Color Backgrounds
- Blue (`#0079bf`)
- Green (`#70b500`)
- Orange (`#ff9f1a`)
- Red (`#eb5a46`)
- Purple (`#c377e0`)
- Pink (`#ff78cb`)
- Lime (`#51e898`)
- Sky (`#00c2e0`)
- Grey (`#c4c9cc`)

### Gradient Backgrounds
- Blue Gradient: `linear-gradient(135deg, #0079bf 0%, #005582 100%)`
- Green Gradient: `linear-gradient(135deg, #70b500 0%, #5a9100 100%)`
- Orange Gradient: `linear-gradient(135deg, #ff9f1a 0%, #e8890f 100%)`
- Purple Gradient: `linear-gradient(135deg, #c377e0 0%, #a855c7 100%)`
- Pink Gradient: `linear-gradient(135deg, #ff78cb 0%, #e85bb8 100%)`

## 🏗️ Architecture

### Core Files

#### 1. Theme Configuration (`src/theme.ts`)
- Extended MUI theme with Trello-specific color tokens
- Light and dark mode support
- Board element styling definitions
- Typography and component overrides

#### 2. Color Utilities (`src/utils/colorUtils.ts`)
- Official Trello color definitions
- Contrast ratio calculations (WCAG compliance)
- Color manipulation functions
- Board background configurations

#### 3. Board Background Hook (`src/hooks/useBoardBackground.ts`)
- Dynamic background management
- CSS custom property injection
- Accessibility validation
- Theme state management

#### 4. Background Selector (`src/components/BoardBackgroundSelector/`)
- Interactive background picker
- Real-time preview
- Accessibility indicators
- Responsive grid layout

## 🎯 Key Features

### 1. Dynamic Theming
- Real-time background changes
- Automatic text color adjustment
- CSS custom properties for performance
- Smooth transitions and animations

### 2. Accessibility Compliance
- WCAG AA contrast ratio validation (4.5:1 minimum)
- Automatic color adjustments for readability
- Screen reader friendly
- High contrast mode support

### 3. Performance Optimized
- CSS custom properties for instant updates
- Minimal re-renders
- Efficient color calculations
- Browser-optimized animations

### 4. Responsive Design
- Mobile-first approach
- Touch-friendly interactions
- Adaptive layouts
- Cross-browser compatibility

## 🔧 Usage

### Setting Board Background
```typescript
import { useBoardBackground } from './hooks/useBoardBackground'

const MyComponent = () => {
  const { setBackground, currentBackground } = useBoardBackground()
  
  // Change to blue gradient background
  setBackground('blue-gradient')
  
  return <div>Current: {currentBackground.name}</div>
}
```

### Using Trello Colors in Components
```typescript
import { useTheme } from '@mui/material/styles'

const MyComponent = () => {
  const theme = useTheme()
  
  return (
    <Box sx={{
      backgroundColor: theme.trello?.colors?.lochmara,
      color: theme.trello?.colors?.silverSand,
    }}>
      Trello-styled content
    </Box>
  )
}
```

### Accessing Color Utilities
```typescript
import { 
  getContrastRatio, 
  meetsWCAGAA, 
  TRELLO_COLORS 
} from './utils/colorUtils'

// Check accessibility
const isAccessible = meetsWCAGAA('#0079bf', '#ffffff') // true

// Get contrast ratio
const ratio = getContrastRatio('#0079bf', '#ffffff') // ~4.6:1

// Use predefined colors
const trelloBlue = TRELLO_COLORS.lochmara.hex // '#0079bf'
```

## 🎨 Component Integration

### BoardBar Component
- Dynamic background based on selected theme
- Adaptive text colors
- Glassmorphism effects
- Interactive background selector

### Navigation Component
- Consistent with board theme
- Responsive color adjustments
- Theme toggle integration

### Card Components
- Trello-inspired styling
- Hover effects and animations
- Label color coordination
- Accessibility-compliant contrast

## 🌙 Dark Mode Support

The implementation includes comprehensive dark mode support:

### Dark Mode Colors
- Background: `#1d2125` (Trello dark background)
- Paper: `#22272b` (Trello dark card background)
- Text Primary: `#b6c2cf` (Trello dark text)
- Text Secondary: `#9fadbc` (Trello dark secondary text)

### Automatic Adjustments
- Text colors adapt to background brightness
- Contrast ratios maintained in both modes
- Smooth transitions between modes
- System preference detection

## 📱 Responsive Behavior

- Mobile-optimized touch targets
- Adaptive grid layouts
- Collapsible navigation elements
- Touch-friendly interactions

## 🔍 Testing

The color scheme implementation includes:
- Automated contrast ratio validation
- Cross-browser compatibility testing
- Accessibility compliance verification
- Performance benchmarking

## 🚀 Future Enhancements

Planned improvements include:
- Custom color picker
- Image-based background extraction
- Advanced color harmony algorithms
- User preference persistence
- Animation customization options

---

This implementation provides a comprehensive, accessible, and performant Trello-like color scheme that enhances the user experience while maintaining design consistency and accessibility standards.
