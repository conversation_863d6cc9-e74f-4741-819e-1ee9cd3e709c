import { createTheme } from '@mui/material/styles'

declare module '@mui/material/styles' {
  interface Theme {
    layout?: {
      navHeight?: string
      boardBarHeight?: string
    }
    trello?: {
      colors?: {
        // Official Trello brand colors
        lochmara?: string
        limeade?: string
        treePoppy?: string
        burntSienna?: string
        schoolBusYellow?: string
        lavender?: string
        hotPink?: string
        robinsEggBlue?: string
        shamrock?: string
        silverSand?: string
      }
      backgrounds?: {
        // Board background options
        blue?: string
        green?: string
        orange?: string
        red?: string
        purple?: string
        pink?: string
        lime?: string
        sky?: string
        grey?: string
        // Gradient backgrounds
        blueGradient?: string
        greenGradient?: string
        orangeGradient?: string
        purpleGradient?: string
        pinkGradient?: string
      }
      boardElements?: {
        listBackground?: string
        cardBackground?: string
        cardHover?: string
        listHeader?: string
        addButton?: string
        addButtonHover?: string
      }
    }
  }
  interface ThemeOptions {
    layout?: {
      navHeight?: string
      boardBarHeight?: string
    }
    trello?: {
      colors?: {
        lochmara?: string
        limeade?: string
        treePoppy?: string
        burntSienna?: string
        schoolBusYellow?: string
        lavender?: string
        hotPink?: string
        robinsEggBlue?: string
        shamrock?: string
        silverSand?: string
      }
      backgrounds?: {
        blue?: string
        green?: string
        orange?: string
        red?: string
        purple?: string
        pink?: string
        lime?: string
        sky?: string
        grey?: string
        blueGradient?: string
        greenGradient?: string
        orangeGradient?: string
        purpleGradient?: string
        pinkGradient?: string
      }
      boardElements?: {
        listBackground?: string
        cardBackground?: string
        cardHover?: string
        listHeader?: string
        addButton?: string
        addButtonHover?: string
      }
    }
  }

  interface TypeBackground {
    header?: string
  }

  interface PaletteBackgroundChannel {
    header?: string
  }
}

const theme = createTheme({
  layout: {
    navHeight: '70px',
    boardBarHeight: '50px',
  },
  // Official Trello color palette
  trello: {
    colors: {
      lochmara: '#0079bf', // Trello Blue (Primary)
      limeade: '#70b500', // Green
      treePoppy: '#ff9f1a', // Orange
      burntSienna: '#eb5a46', // Red
      schoolBusYellow: '#f2d600', // Yellow
      lavender: '#c377e0', // Purple
      hotPink: '#ff78cb', // Pink
      robinsEggBlue: '#00c2e0', // Light Blue
      shamrock: '#51e898', // Light Green
      silverSand: '#c4c9cc', // Grey
    },
    backgrounds: {
      // Solid color backgrounds
      blue: '#0079bf',
      green: '#70b500',
      orange: '#ff9f1a',
      red: '#eb5a46',
      purple: '#c377e0',
      pink: '#ff78cb',
      lime: '#51e898',
      sky: '#00c2e0',
      grey: '#c4c9cc',
      // Gradient backgrounds
      blueGradient: 'linear-gradient(135deg, #0079bf 0%, #005582 100%)',
      greenGradient: 'linear-gradient(135deg, #70b500 0%, #5a9100 100%)',
      orangeGradient: 'linear-gradient(135deg, #ff9f1a 0%, #e8890f 100%)',
      purpleGradient: 'linear-gradient(135deg, #c377e0 0%, #a855c7 100%)',
      pinkGradient: 'linear-gradient(135deg, #ff78cb 0%, #e85bb8 100%)',
    },
    boardElements: {
      listBackground: '#ebecf0',
      cardBackground: '#ffffff',
      cardHover: '#f4f5f7',
      listHeader: '#5e6c84',
      addButton: '#ddd',
      addButtonHover: '#ccc',
    },
  },
  cssVariables: {
    colorSchemeSelector: 'data-mui-color-scheme',
  },
  colorSchemes: {
    light: {
      palette: {
        primary: {
          main: '#0079bf', // Trello Blue
          light: '#4da6d9',
          dark: '#005582',
          contrastText: '#ffffff',
        },
        secondary: {
          main: '#026aa7',
          light: '#4da6d9',
          dark: '#004a7c',
          contrastText: '#ffffff',
        },
        background: {
          default: '#f4f5f7', // Trello light grey background
          paper: '#ffffff',
          header: '#ffffff',
        },
        text: {
          primary: '#172b4d', // Trello dark text
          secondary: '#5e6c84', // Trello secondary text
        },
        success: {
          main: '#70b500', // Trello green
        },
        warning: {
          main: '#f2d600', // Trello yellow
        },
        error: {
          main: '#eb5a46', // Trello red
        },
        info: {
          main: '#00c2e0', // Trello light blue
        },
      },
    },
    dark: {
      palette: {
        primary: {
          main: '#4da6d9', // Lighter Trello blue for dark mode
          light: '#7bc4e8',
          dark: '#0079bf',
          contrastText: '#ffffff',
        },
        secondary: {
          main: '#4da6d9',
          light: '#7bc4e8',
          dark: '#026aa7',
          contrastText: '#ffffff',
        },
        background: {
          default: '#1d2125', // Trello dark background
          paper: '#22272b', // Trello dark card background
          header: '#1d2125', // Consistent with default
        },
        text: {
          primary: '#b6c2cf', // Trello dark mode text
          secondary: '#9fadbc', // Trello dark mode secondary text
        },
        success: {
          main: '#70b500', // Trello green (same as light)
        },
        warning: {
          main: '#f2d600', // Trello yellow (same as light)
        },
        error: {
          main: '#eb5a46', // Trello red (same as light)
        },
        info: {
          main: '#00c2e0', // Trello light blue (same as light)
        },
      },
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Helvetica Neue',
      'Arial',
      'sans-serif',
    ].join(','),
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
    h1: {
      fontSize: '2rem',
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.6,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.43,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.75,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.57,
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.66,
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 2.66,
      textTransform: 'uppercase',
      letterSpacing: '0.08333em',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 500,
          fontFamily:
            'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 8,
          boxShadow: theme.vars
            ? 'var(--mui-shadows-1)'
            : '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          transition: 'box-shadow 0.3s ease-in-out',
          '&:hover': {
            boxShadow: theme.vars
              ? 'var(--mui-shadows-3)'
              : '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
          },
          // Enhanced dark mode shadows using CSS variables
          '[data-mui-color-scheme="dark"] &': {
            boxShadow: '0 1px 3px rgba(0,0,0,0.3), 0 1px 2px rgba(0,0,0,0.4)',
            '&:hover': {
              boxShadow: '0 3px 6px rgba(0,0,0,0.4), 0 3px 6px rgba(0,0,0,0.5)',
            },
          },
        }),
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: ({ theme }) => ({
          boxShadow: theme.vars
            ? 'var(--mui-shadows-1)'
            : '0 1px 3px rgba(0,0,0,0.12)',
          transition: 'box-shadow 0.3s ease-in-out',
          '[data-mui-color-scheme="dark"] &': {
            boxShadow: '0 1px 3px rgba(0,0,0,0.3)',
          },
        }),
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: ({ theme }) => ({
          transition:
            'background-color 0.3s ease-in-out, background-image 0.3s ease-in-out',
          '[data-mui-color-scheme="dark"] &': {
            backgroundImage: theme.vars
              ? 'var(--mui-overlays-1)'
              : 'linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))',
          },
        }),
      },
    },
    // MuiTextField: {
    //   styleOverrides: {
    //     root: ({ theme }) => ({
    //       // Input label styling with higher specificity
    //       '&.MuiTextField-root .MuiInputLabel-outlined': {
    //         color: theme.vars
    //           ? 'var(--mui-palette-primary-main) !important'
    //           : `${theme.palette.primary.main} !important`,
    //         '&.Mui-focused': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-secondary-main) !important'
    //             : `${theme.palette.secondary.main} !important`,
    //         },
    //       },
    //       // Input text styling with higher specificity
    //       '&.MuiTextField-root .MuiInputBase-input': {
    //         color: theme.vars
    //           ? 'var(--mui-palette-primary-main) !important'
    //           : `${theme.palette.primary.main} !important`,
    //       },
    //       // Clear icon and input adornments styling
    //       '&.MuiTextField-root .MuiInputAdornment-root': {
    //         color: theme.vars
    //           ? 'var(--mui-palette-primary-main) !important'
    //           : `${theme.palette.primary.main} !important`,
    //         '& .MuiIconButton-root': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-primary-main) !important'
    //             : `${theme.palette.primary.main} !important`,
    //           '&:hover': {
    //             backgroundColor: theme.vars
    //               ? 'var(--mui-palette-primary-main) !important'
    //               : `${theme.palette.primary.main} !important`,
    //             opacity: '0.1 !important',
    //             color: theme.vars
    //               ? 'var(--mui-palette-primary-dark) !important'
    //               : `${theme.palette.primary.dark} !important`,
    //           },
    //         },
    //         '& .MuiSvgIcon-root': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-primary-main) !important'
    //             : `${theme.palette.primary.main} !important`,
    //         },
    //       },
    //       // Border styling for outlined variant with higher specificity
    //       '&.MuiTextField-root .MuiOutlinedInput-notchedOutline': {
    //         borderColor: theme.vars
    //           ? 'var(--mui-palette-primary-main) !important'
    //           : `${theme.palette.primary.main} !important`,
    //         borderWidth: '2px !important',
    //       },
    //       // Hover state with higher specificity
    //       '&.MuiTextField-root .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline':
    //         {
    //           borderColor: theme.vars
    //             ? 'var(--mui-palette-primary-dark) !important'
    //             : `${theme.palette.primary.dark} !important`,
    //           borderWidth: '2px !important',
    //         },
    //       // Focused state with higher specificity
    //       '&.MuiTextField-root .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline':
    //         {
    //           borderColor: theme.vars
    //             ? 'var(--mui-palette-secondary-main) !important'
    //             : `${theme.palette.secondary.main} !important`,
    //           borderWidth: '3px !important',
    //         },
    //       // Error state with higher specificity
    //       '&.MuiTextField-root .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline':
    //         {
    //           borderColor: theme.vars
    //             ? 'var(--mui-palette-error-main) !important'
    //             : `${theme.palette.error.main} !important`,
    //           borderWidth: '2px !important',
    //         },
    //       // Helper text styling
    //       '& .MuiFormHelperText-root': {
    //         color: theme.vars
    //           ? 'var(--mui-palette-text-secondary)'
    //           : theme.palette.text.secondary,
    //         margin: '4px 0 0 0',
    //         '&.Mui-error': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-error-main)'
    //             : theme.palette.error.main,
    //         },
    //       },
    //       // Dark mode specific adjustments
    //       '[data-mui-color-scheme="dark"] &': {
    //         '& .MuiInputLabel-outlined': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-primary-light) !important'
    //             : `${theme.palette.primary.light} !important`,
    //         },
    //         '& .MuiInputBase-input': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-text-primary) !important'
    //             : `${theme.palette.text.primary} !important`,
    //         },
    //         '& .MuiInputAdornment-root': {
    //           color: theme.vars
    //             ? 'var(--mui-palette-primary-light) !important'
    //             : `${theme.palette.primary.light} !important`,
    //           '& .MuiIconButton-root': {
    //             color: theme.vars
    //               ? 'var(--mui-palette-primary-light) !important'
    //               : `${theme.palette.primary.light} !important`,
    //             '&:hover': {
    //               backgroundColor: theme.vars
    //                 ? 'var(--mui-palette-primary-light) !important'
    //                 : `${theme.palette.primary.light} !important`,
    //               opacity: '0.1 !important',
    //             },
    //           },
    //           '& .MuiSvgIcon-root': {
    //             color: theme.vars
    //               ? 'var(--mui-palette-primary-light) !important'
    //               : `${theme.palette.primary.light} !important`,
    //           },
    //         },
    //       },
    //     }),
    //   },
    // },
  },
})

export default theme
