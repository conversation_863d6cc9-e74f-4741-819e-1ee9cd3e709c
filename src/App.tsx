import React from 'react'
import { Box, Container } from '@mui/material'
import Navigation from './components/Navigation'
import BoardBar from './components/BoardBar/BoardBar'

const App: React.FC = () => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}
    >
      <Container disableGutters maxWidth={false} sx={{ minHeight: '100vh' }}>
        <Navigation />
        <BoardBar />
        <Box
          sx={{
            minHeight: theme =>
              `calc(100vh - (${theme.layout?.navHeight} + ${theme.layout?.boardBarHeight}))`,
            p: 3,
            display: 'flex',
            gap: 3,
            overflowX: 'auto',
          }}
        >
          {/* Beautiful board lists */}
          {['To Do', 'In Progress', 'Done'].map(title => (
            <Box
              key={title}
              sx={{
                minWidth: 300,
                maxWidth: 300,
                backgroundColor: 'rgba(255,255,255,0.95)',
                borderRadius: 3,
                p: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box
                  sx={{
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: '#172b4d',
                    mb: 2,
                  }}
                >
                  {title}
                </Box>

                {/* Sample cards */}
                {[1, 2].map(cardIndex => (
                  <Box
                    key={cardIndex}
                    sx={{
                      backgroundColor: '#ffffff',
                      borderRadius: 2,
                      p: 2,
                      mb: 1.5,
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      cursor: 'pointer',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                      },
                      transition: 'all 0.2s ease-in-out',
                    }}
                  >
                    <Box
                      sx={{
                        fontSize: '0.875rem',
                        fontWeight: 500,
                        color: '#172b4d',
                      }}
                    >
                      Beautiful Card {cardIndex}
                    </Box>
                    <Box
                      sx={{ fontSize: '0.75rem', color: '#5e6c84', mt: 0.5 }}
                    >
                      This is a sample card with beautiful styling
                    </Box>
                  </Box>
                ))}

                <Box
                  sx={{
                    p: 1.5,
                    borderRadius: 2,
                    border: '2px dashed #ddd',
                    textAlign: 'center',
                    color: '#5e6c84',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: '#0079bf',
                      color: '#0079bf',
                      backgroundColor: 'rgba(0,121,191,0.05)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  + Add a card
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
      </Container>
    </Box>
  )
}

export default App
