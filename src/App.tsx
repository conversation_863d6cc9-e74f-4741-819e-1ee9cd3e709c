import React from 'react'
import { Box, Container } from '@mui/material'
import Navigation from './components/Navigation'
import BoardBar from './components/BoardBar/BoardBar'
import TrelloColorDemo from './components/TrelloColorDemo'
import { useBoardBackground } from './hooks/useBoardBackground'

const App: React.FC = () => {
  const { backgroundStyle } = useBoardBackground()

  return (
    <Box sx={backgroundStyle}>
      <Container disableGutters maxWidth={false} sx={{ minHeight: '100vh' }}>
        <Navigation />
        <BoardBar />
        <Box
          sx={{
            minHeight: theme =>
              `calc(100vh - (${theme.layout?.navHeight} + ${theme.layout?.boardBarHeight}))`,
            backgroundColor: 'rgba(255,255,255,0.05)',
            backdropFilter: 'blur(10px)',
          }}
        >
          <TrelloColorDemo />
        </Box>
      </Container>
    </Box>
  )
}

export default App
