/**
 * Color utilities for Trello-like theming
 * Includes contrast calculation, color manipulation, and accessibility helpers
 */

export interface TrelloColor {
  name: string
  hex: string
  rgb: { r: number; g: number; b: number }
  hsl: { h: number; s: number; l: number }
}

export interface BoardBackground {
  id: string
  name: string
  type: 'color' | 'gradient'
  value: string
  textColor: 'light' | 'dark'
  dominantColor: string
}

// Official Trello color palette
export const TRELLO_COLORS: Record<string, TrelloColor> = {
  lochmara: {
    name: 'Loch<PERSON>',
    hex: '#0079bf',
    rgb: { r: 0, g: 121, b: 191 },
    hsl: { h: 208, s: 100, l: 37 },
  },
  limeade: {
    name: '<PERSON>ead<PERSON>',
    hex: '#70b500',
    rgb: { r: 112, g: 181, b: 0 },
    hsl: { h: 83, s: 100, l: 35 },
  },
  treePoppy: {
    name: 'Tree Poppy',
    hex: '#ff9f1a',
    rgb: { r: 255, g: 159, b: 26 },
    hsl: { h: 35, s: 100, l: 55 },
  },
  burntSienna: {
    name: '<PERSON>t <PERSON>',
    hex: '#eb5a46',
    rgb: { r: 235, g: 90, b: 70 },
    hsl: { h: 7, s: 81, l: 60 },
  },
  schoolBusYellow: {
    name: 'School Bus Yellow',
    hex: '#f2d600',
    rgb: { r: 242, g: 214, b: 0 },
    hsl: { h: 53, s: 100, l: 47 },
  },
  lavender: {
    name: 'Lavender',
    hex: '#c377e0',
    rgb: { r: 195, g: 119, b: 224 },
    hsl: { h: 283, s: 62, l: 67 },
  },
  hotPink: {
    name: 'Hot Pink',
    hex: '#ff78cb',
    rgb: { r: 255, g: 120, b: 203 },
    hsl: { h: 323, s: 100, l: 74 },
  },
  robinsEggBlue: {
    name: "Robin's Egg Blue",
    hex: '#00c2e0',
    rgb: { r: 0, g: 194, b: 224 },
    hsl: { h: 188, s: 100, l: 44 },
  },
  shamrock: {
    name: 'Shamrock',
    hex: '#51e898',
    rgb: { r: 81, g: 232, b: 152 },
    hsl: { h: 148, s: 77, l: 61 },
  },
  silverSand: {
    name: 'Silver Sand',
    hex: '#c4c9cc',
    rgb: { r: 196, g: 201, b: 204 },
    hsl: { h: 203, s: 8, l: 78 },
  },
}

// Predefined board backgrounds
export const BOARD_BACKGROUNDS: BoardBackground[] = [
  {
    id: 'blue',
    name: 'Blue',
    type: 'color',
    value: '#0079bf',
    textColor: 'light',
    dominantColor: '#0079bf',
  },
  {
    id: 'green',
    name: 'Green',
    type: 'color',
    value: '#70b500',
    textColor: 'light',
    dominantColor: '#70b500',
  },
  {
    id: 'orange',
    name: 'Orange',
    type: 'color',
    value: '#ff9f1a',
    textColor: 'dark',
    dominantColor: '#ff9f1a',
  },
  {
    id: 'red',
    name: 'Red',
    type: 'color',
    value: '#eb5a46',
    textColor: 'light',
    dominantColor: '#eb5a46',
  },
  {
    id: 'purple',
    name: 'Purple',
    type: 'color',
    value: '#c377e0',
    textColor: 'dark',
    dominantColor: '#c377e0',
  },
  {
    id: 'pink',
    name: 'Pink',
    type: 'color',
    value: '#ff78cb',
    textColor: 'dark',
    dominantColor: '#ff78cb',
  },
  {
    id: 'lime',
    name: 'Lime',
    type: 'color',
    value: '#51e898',
    textColor: 'dark',
    dominantColor: '#51e898',
  },
  {
    id: 'sky',
    name: 'Sky',
    type: 'color',
    value: '#00c2e0',
    textColor: 'dark',
    dominantColor: '#00c2e0',
  },
  {
    id: 'grey',
    name: 'Grey',
    type: 'color',
    value: '#c4c9cc',
    textColor: 'dark',
    dominantColor: '#c4c9cc',
  },
  {
    id: 'blue-gradient',
    name: 'Blue Gradient',
    type: 'gradient',
    value: 'linear-gradient(135deg, #0079bf 0%, #005582 100%)',
    textColor: 'light',
    dominantColor: '#0079bf',
  },
  {
    id: 'green-gradient',
    name: 'Green Gradient',
    type: 'gradient',
    value: 'linear-gradient(135deg, #70b500 0%, #5a9100 100%)',
    textColor: 'light',
    dominantColor: '#70b500',
  },
  {
    id: 'orange-gradient',
    name: 'Orange Gradient',
    type: 'gradient',
    value: 'linear-gradient(135deg, #ff9f1a 0%, #e8890f 100%)',
    textColor: 'dark',
    dominantColor: '#ff9f1a',
  },
  {
    id: 'purple-gradient',
    name: 'Purple Gradient',
    type: 'gradient',
    value: 'linear-gradient(135deg, #c377e0 0%, #a855c7 100%)',
    textColor: 'light',
    dominantColor: '#c377e0',
  },
  {
    id: 'pink-gradient',
    name: 'Pink Gradient',
    type: 'gradient',
    value: 'linear-gradient(135deg, #ff78cb 0%, #e85bb8 100%)',
    textColor: 'dark',
    dominantColor: '#ff78cb',
  },
]

/**
 * Convert hex color to RGB
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

/**
 * Convert RGB to hex
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

/**
 * Calculate relative luminance of a color
 * Used for contrast ratio calculations
 */
export const getRelativeLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

/**
 * Calculate contrast ratio between two colors
 * Returns a value between 1 and 21
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1)
  const rgb2 = hexToRgb(color2)
  
  if (!rgb1 || !rgb2) return 1
  
  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b)
  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b)
  
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * Check if color combination meets WCAG AA accessibility standards
 */
export const meetsWCAGAA = (backgroundColor: string, textColor: string): boolean => {
  return getContrastRatio(backgroundColor, textColor) >= 4.5
}

/**
 * Check if color combination meets WCAG AAA accessibility standards
 */
export const meetsWCAGAAA = (backgroundColor: string, textColor: string): boolean => {
  return getContrastRatio(backgroundColor, textColor) >= 7
}

/**
 * Determine if a color is light or dark
 */
export const isLightColor = (color: string): boolean => {
  const rgb = hexToRgb(color)
  if (!rgb) return false
  
  const luminance = getRelativeLuminance(rgb.r, rgb.g, rgb.b)
  return luminance > 0.5
}

/**
 * Get appropriate text color (black or white) for a background color
 */
export const getTextColorForBackground = (backgroundColor: string): string => {
  return isLightColor(backgroundColor) ? '#172b4d' : '#ffffff'
}

/**
 * Lighten a color by a percentage
 */
export const lightenColor = (color: string, percent: number): string => {
  const rgb = hexToRgb(color)
  if (!rgb) return color
  
  const { r, g, b } = rgb
  const newR = Math.min(255, Math.round(r + (255 - r) * (percent / 100)))
  const newG = Math.min(255, Math.round(g + (255 - g) * (percent / 100)))
  const newB = Math.min(255, Math.round(b + (255 - b) * (percent / 100)))
  
  return rgbToHex(newR, newG, newB)
}

/**
 * Darken a color by a percentage
 */
export const darkenColor = (color: string, percent: number): string => {
  const rgb = hexToRgb(color)
  if (!rgb) return color
  
  const { r, g, b } = rgb
  const newR = Math.max(0, Math.round(r * (1 - percent / 100)))
  const newG = Math.max(0, Math.round(g * (1 - percent / 100)))
  const newB = Math.max(0, Math.round(b * (1 - percent / 100)))
  
  return rgbToHex(newR, newG, newB)
}
