import React, { useState } from 'react'
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Paper,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  Palette as PaletteIcon,
  ContentCopy as CopyIcon,
  Check as CheckIcon,
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import { useBoardBackground } from '../../hooks/useBoardBackground'
import { TRELLO_COLORS, BOARD_BACKGROUNDS } from '../../utils/colorUtils'

const TrelloColorDemo: React.FC = () => {
  const theme = useTheme()
  const { currentBackground, setBackground, contrastRatio, isAccessible } = useBoardBackground()
  const [copiedColor, setCopiedColor] = useState<string | null>(null)

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedColor(text)
      setTimeout(() => setCopiedColor(null), 2000)
    } catch (err) {
      console.error('Failed to copy: ', err)
    }
  }

  const ColorCard: React.FC<{ name: string; hex: string; description?: string }> = ({
    name,
    hex,
    description,
  }) => (
    <Card
      sx={{
        height: '100%',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 3,
        },
      }}
      onClick={() => copyToClipboard(hex)}
    >
      <Box
        sx={{
          height: 80,
          backgroundColor: hex,
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {copiedColor === hex ? (
          <CheckIcon sx={{ color: 'white', fontSize: 24 }} />
        ) : (
          <CopyIcon sx={{ color: 'white', opacity: 0.8, fontSize: 20 }} />
        )}
      </Box>
      <CardContent sx={{ p: 2 }}>
        <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
          {name}
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          {hex.toUpperCase()}
        </Typography>
        {description && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
            {description}
          </Typography>
        )}
      </CardContent>
    </Card>
  )

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PaletteIcon color="primary" />
          Trello Color Scheme Demo
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Official Trello colors with dynamic backgrounds and accessibility features
        </Typography>
      </Box>

      {/* Current Background Info */}
      <Paper sx={{ p: 3, mb: 4, backgroundColor: 'background.paper' }}>
        <Typography variant="h6" gutterBottom>
          Current Background: {currentBackground.name}
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <Chip
            label={`Type: ${currentBackground.type}`}
            variant="outlined"
            size="small"
          />
          <Chip
            label={`Text: ${currentBackground.textColor}`}
            variant="outlined"
            size="small"
          />
          <Chip
            label={`Contrast: ${contrastRatio.toFixed(1)}:1`}
            color={isAccessible ? 'success' : 'warning'}
            size="small"
          />
          <Chip
            label={isAccessible ? 'WCAG AA ✓' : 'WCAG AA ✗'}
            color={isAccessible ? 'success' : 'error'}
            size="small"
          />
        </Box>
      </Paper>

      {/* Official Trello Colors */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          Official Trello Colors
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Click any color to copy its hex code
        </Typography>
        <Grid container spacing={2}>
          {Object.entries(TRELLO_COLORS).map(([key, color]) => (
            <Grid item xs={6} sm={4} md={3} lg={2.4} key={key}>
              <ColorCard
                name={color.name}
                hex={color.hex}
                description={`RGB: ${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b}`}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Board Backgrounds */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          Board Backgrounds
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Click any background to apply it to the current board
        </Typography>
        <Grid container spacing={2}>
          {BOARD_BACKGROUNDS.map((background) => (
            <Grid item xs={6} sm={4} md={3} key={background.id}>
              <Card
                sx={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                  border: currentBackground.id === background.id ? '2px solid' : '1px solid transparent',
                  borderColor: currentBackground.id === background.id ? 'primary.main' : 'transparent',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 3,
                  },
                }}
                onClick={() => setBackground(background.id)}
              >
                <Box
                  sx={{
                    height: 60,
                    background: background.value,
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {currentBackground.id === background.id && (
                    <CheckIcon
                      sx={{
                        color: background.textColor === 'light' ? '#ffffff' : '#172b4d',
                        fontSize: 24,
                      }}
                    />
                  )}
                </Box>
                <CardContent sx={{ p: 1.5 }}>
                  <Typography variant="caption" fontWeight="bold">
                    {background.name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                    <Chip
                      label={background.type}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.6rem', height: 16 }}
                    />
                    <Chip
                      label={background.textColor}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.6rem', height: 16 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Theme Colors in Use */}
      <Box>
        <Typography variant="h5" gutterBottom>
          Current Theme Colors
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Primary"
              hex={theme.palette.primary.main}
              description="Main brand color"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Secondary"
              hex={theme.palette.secondary.main}
              description="Secondary brand color"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Success"
              hex={theme.palette.success.main}
              description="Success states"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Error"
              hex={theme.palette.error.main}
              description="Error states"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Warning"
              hex={theme.palette.warning.main}
              description="Warning states"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Info"
              hex={theme.palette.info.main}
              description="Info states"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Background"
              hex={theme.palette.background.default}
              description="Default background"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <ColorCard
              name="Paper"
              hex={theme.palette.background.paper}
              description="Card background"
            />
          </Grid>
        </Grid>
      </Box>

      {copiedColor && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            backgroundColor: 'success.main',
            color: 'white',
            px: 2,
            py: 1,
            borderRadius: 1,
            boxShadow: 3,
          }}
        >
          Copied {copiedColor}!
        </Box>
      )}
    </Box>
  )
}

export default TrelloColorDemo
