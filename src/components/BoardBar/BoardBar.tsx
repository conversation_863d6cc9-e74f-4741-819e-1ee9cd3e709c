import React from 'react'
import { <PERSON>, Chip, IconButton, Tooltip } from '@mui/material'
import {
  VpnLock as VpnLockIcon,
  Dashboard as DashboardIcon,
  Star as StarIcon,
  MoreHoriz as MoreHorizIcon,
} from '@mui/icons-material'

const BoardBar: React.FC = () => {
  return (
    <Box
      sx={{
        height: theme => theme.layout?.boardBarHeight,
        background: 'linear-gradient(135deg, #0079bf 0%, #42a5f5 100%)',
        px: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 2px 8px rgba(0,121,191,0.2)',
      }}
    >
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <Chip
          label='Beautiful Trello Board'
          clickable
          icon={<DashboardIcon />}
          sx={{
            color: '#0079bf',
            bgcolor: 'rgba(255,255,255,0.95)',
            border: 'none',
            px: '12px',
            borderRadius: '8px',
            fontWeight: 600,
            fontSize: '0.875rem',
            '& .MuiSvgIcon-root': {
              color: '#0079bf',
            },
            '&:hover': {
              bgcolor: '#ffffff',
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            },
            transition: 'all 0.2s ease-in-out',
          }}
        />
        <Chip
          label='Public Workspace'
          clickable
          icon={<VpnLockIcon />}
          sx={{
            color: '#ffffff',
            bgcolor: 'rgba(255,255,255,0.2)',
            border: '1px solid rgba(255,255,255,0.3)',
            px: '12px',
            borderRadius: '8px',
            '& .MuiSvgIcon-root': {
              color: '#ffffff',
            },
            '&:hover': {
              bgcolor: 'rgba(255,255,255,0.3)',
              transform: 'translateY(-1px)',
            },
            transition: 'all 0.2s ease-in-out',
          }}
        />
      </Box>

      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <Tooltip title='Star board'>
          <IconButton
            sx={{
              color: '#ffffff',
              bgcolor: 'rgba(255,255,255,0.15)',
              '&:hover': {
                bgcolor: 'rgba(255,255,255,0.25)',
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
            size='small'
          >
            <StarIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title='Board menu'>
          <IconButton
            sx={{
              color: '#ffffff',
              bgcolor: 'rgba(255,255,255,0.15)',
              '&:hover': {
                bgcolor: 'rgba(255,255,255,0.25)',
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
            size='small'
          >
            <MoreHorizIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  )
}

export default BoardBar
