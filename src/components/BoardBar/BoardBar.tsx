import { Box, Chip } from '@mui/material'
import VpnLockIcon from '@mui/icons-material/VpnLock'
import DashboardIcon from '@mui/icons-material/Dashboard'

const BoardBar = () => {
  return (
    <Box
      sx={{
        height: theme => theme.layout?.boardBarHeight,
        backgroundColor: 'primary.main',
        px: 2,
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Chip
        label='Public/Private Workspace'
        clickable
        icon={<VpnLockIcon />}
        sx={{
          color: 'primary.main',
          bgcolor: 'white',
          border: 'none',
          px: '5px',
          borderRadius: '4px',
          '& .MuiSvgIcon-root': {
            color: 'primary.main',
          },
          '& :hover': {
            bgColor: 'primary.main',
          },
        }}
      />
      <Chip
        label='Public/Private Workspace'
        clickable
        icon={<VpnLockIcon />}
        sx={{
          color: 'primary.main',
          bgcolor: 'white',
          border: 'none',
          px: '5px',
          borderRadius: '4px',
          '& .MuiSvgIcon-root': {
            color: 'primary.main',
          },
          '& :hover': {
            bgColor: 'primary.main',
          },
        }}
      />
    </Box>
  )
}

export default BoardBar
