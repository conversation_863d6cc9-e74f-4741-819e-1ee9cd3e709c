import React, { useState } from 'react'
import { Box, Chip, IconButton, Tooltip } from '@mui/material'
import {
  VpnLock as VpnLockIcon,
  Dashboard as DashboardIcon,
  <PERSON>lette as PaletteIcon,
  Star as StarIcon,
  MoreHoriz as MoreHorizIcon,
} from '@mui/icons-material'
import { useBoardBackground } from '../../hooks/useBoardBackground'
import BoardBackgroundSelector from '../BoardBackgroundSelector'

const BoardBar: React.FC = () => {
  const { currentBackground, isDarkBackground } = useBoardBackground()
  const [backgroundSelectorOpen, setBackgroundSelectorOpen] = useState(false)

  // Dynamic text color based on background
  const textColor = isDarkBackground ? '#ffffff' : '#172b4d'
  const chipBgColor = isDarkBackground
    ? 'rgba(255,255,255,0.2)'
    : 'rgba(255,255,255,0.9)'
  const chipTextColor = isDarkBackground ? '#ffffff' : '#172b4d'

  return (
    <>
      <Box
        sx={{
          height: theme => theme.layout?.boardBarHeight,
          background: `linear-gradient(90deg, ${currentBackground.dominantColor}dd, ${currentBackground.dominantColor}aa)`,
          px: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <Chip
            label='Trello Board'
            clickable
            icon={<DashboardIcon />}
            sx={{
              color: chipTextColor,
              bgcolor: chipBgColor,
              border: 'none',
              px: '8px',
              borderRadius: '6px',
              fontWeight: 600,
              '& .MuiSvgIcon-root': {
                color: chipTextColor,
              },
              '&:hover': {
                bgcolor: isDarkBackground
                  ? 'rgba(255,255,255,0.3)'
                  : 'rgba(255,255,255,1)',
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          />
          <Chip
            label='Public Workspace'
            clickable
            icon={<VpnLockIcon />}
            sx={{
              color: chipTextColor,
              bgcolor: chipBgColor,
              border: 'none',
              px: '8px',
              borderRadius: '6px',
              '& .MuiSvgIcon-root': {
                color: chipTextColor,
              },
              '&:hover': {
                bgcolor: isDarkBackground
                  ? 'rgba(255,255,255,0.3)'
                  : 'rgba(255,255,255,1)',
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <Tooltip title='Change background'>
            <IconButton
              onClick={() => setBackgroundSelectorOpen(true)}
              sx={{
                color: textColor,
                bgcolor: chipBgColor,
                '&:hover': {
                  bgcolor: isDarkBackground
                    ? 'rgba(255,255,255,0.3)'
                    : 'rgba(255,255,255,1)',
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
              size='small'
            >
              <PaletteIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title='Star board'>
            <IconButton
              sx={{
                color: textColor,
                bgcolor: chipBgColor,
                '&:hover': {
                  bgcolor: isDarkBackground
                    ? 'rgba(255,255,255,0.3)'
                    : 'rgba(255,255,255,1)',
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
              size='small'
            >
              <StarIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title='Board menu'>
            <IconButton
              sx={{
                color: textColor,
                bgcolor: chipBgColor,
                '&:hover': {
                  bgcolor: isDarkBackground
                    ? 'rgba(255,255,255,0.3)'
                    : 'rgba(255,255,255,1)',
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
              size='small'
            >
              <MoreHorizIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <BoardBackgroundSelector
        open={backgroundSelectorOpen}
        onClose={() => setBackgroundSelectorOpen(false)}
      />
    </>
  )
}

export default BoardBar
