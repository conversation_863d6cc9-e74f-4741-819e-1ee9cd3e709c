import React, { useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  Grid,
  Typography,
  IconButton,
  Tooltip,
  Paper,
  Chip,
} from '@mui/material'
import {
  Close as CloseIcon,
  Palette as PaletteIcon,
  Check as CheckIcon,
} from '@mui/icons-material'
import { useBoardBackground } from '../../hooks/useBoardBackground'
import { BoardBackground } from '../../utils/colorUtils'

interface BoardBackgroundSelectorProps {
  open: boolean
  onClose: () => void
}

const BoardBackgroundSelector: React.FC<BoardBackgroundSelectorProps> = ({
  open,
  onClose,
}) => {
  const {
    currentBackground,
    setBackground,
    getAllBackgrounds,
    contrastRatio,
    isAccessible,
  } = useBoardBackground()

  const backgrounds = getAllBackgrounds()
  const [selectedBackground, setSelectedBackground] = useState<BoardBackground>(currentBackground)

  const handleBackgroundSelect = (background: BoardBackground) => {
    setSelectedBackground(background)
    setBackground(background.id)
  }

  const handleClose = () => {
    onClose()
  }

  const renderBackgroundOption = (background: BoardBackground) => {
    const isSelected = selectedBackground.id === background.id
    
    return (
      <Grid item xs={6} sm={4} md={3} key={background.id}>
        <Paper
          elevation={isSelected ? 8 : 2}
          sx={{
            position: 'relative',
            cursor: 'pointer',
            borderRadius: 2,
            overflow: 'hidden',
            transition: 'all 0.3s ease-in-out',
            border: isSelected ? '3px solid' : '2px solid transparent',
            borderColor: isSelected ? 'primary.main' : 'transparent',
            '&:hover': {
              elevation: 6,
              transform: 'translateY(-2px)',
            },
          }}
          onClick={() => handleBackgroundSelect(background)}
        >
          <Box
            sx={{
              height: 80,
              background: background.value,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
            }}
          >
            {isSelected && (
              <CheckIcon
                sx={{
                  color: background.textColor === 'light' ? '#ffffff' : '#172b4d',
                  fontSize: 32,
                  backgroundColor: 'rgba(0,0,0,0.2)',
                  borderRadius: '50%',
                  p: 0.5,
                }}
              />
            )}
          </Box>
          <Box sx={{ p: 1 }}>
            <Typography
              variant="caption"
              sx={{
                fontWeight: isSelected ? 600 : 400,
                color: isSelected ? 'primary.main' : 'text.primary',
              }}
            >
              {background.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
              <Chip
                label={background.type}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.6rem', height: 16 }}
              />
              {background.textColor === 'light' && (
                <Chip
                  label="Dark"
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.6rem', height: 16 }}
                />
              )}
            </Box>
          </Box>
        </Paper>
      </Grid>
    )
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '80vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PaletteIcon color="primary" />
          <Typography variant="h6" component="div">
            Board Background
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {/* Current Selection Info */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Current Selection: {selectedBackground.name}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              label={`Contrast: ${contrastRatio.toFixed(1)}:1`}
              color={isAccessible ? 'success' : 'warning'}
              size="small"
            />
            <Chip
              label={isAccessible ? 'WCAG AA ✓' : 'WCAG AA ✗'}
              color={isAccessible ? 'success' : 'error'}
              size="small"
            />
          </Box>
        </Box>

        {/* Color Backgrounds */}
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, mb: 1 }}>
          Solid Colors
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {backgrounds
            .filter(bg => bg.type === 'color')
            .map(renderBackgroundOption)}
        </Grid>

        {/* Gradient Backgrounds */}
        <Typography variant="subtitle1" gutterBottom sx={{ mb: 1 }}>
          Gradients
        </Typography>
        <Grid container spacing={2}>
          {backgrounds
            .filter(bg => bg.type === 'gradient')
            .map(renderBackgroundOption)}
        </Grid>

        {/* Accessibility Note */}
        <Box
          sx={{
            mt: 3,
            p: 2,
            backgroundColor: 'background.paper',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            <strong>Accessibility Note:</strong> All backgrounds are designed to meet WCAG AA 
            contrast standards. The contrast ratio shows how readable text will be on the 
            selected background.
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default BoardBackgroundSelector
