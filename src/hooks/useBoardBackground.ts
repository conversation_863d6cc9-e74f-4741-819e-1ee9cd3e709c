import { useState, useEffect, useCallback } from 'react'
import { useTheme } from '@mui/material/styles'
import { 
  BoardBackground, 
  BOARD_BACKGROUNDS, 
  getTextColorForBackground,
  getContrastRatio,
  meetsWCAGAA 
} from '../utils/colorUtils'

interface UseBoardBackgroundReturn {
  // Current background state
  currentBackground: BoardBackground
  backgroundStyle: React.CSSProperties
  textColor: string
  isDarkBackground: boolean
  
  // Background management
  setBackground: (backgroundId: string) => void
  getBackgroundById: (id: string) => BoardBackground | undefined
  getAllBackgrounds: () => BoardBackground[]
  
  // Dynamic theming
  applyDynamicTheme: (background: BoardBackground) => void
  resetTheme: () => void
  
  // Accessibility
  contrastRatio: number
  isAccessible: boolean
}

/**
 * Custom hook for managing board backgrounds and dynamic theming
 * Provides Trello-like board background functionality with accessibility support
 */
export const useBoardBackground = (): UseBoardBackgroundReturn => {
  const theme = useTheme()
  const [currentBackground, setCurrentBackground] = useState<BoardBackground>(
    BOARD_BACKGROUNDS[0] // Default to blue background
  )
  const [textColor, setTextColor] = useState<string>('#ffffff')
  const [contrastRatio, setContrastRatio] = useState<number>(21)

  // Calculate background style based on current background
  const backgroundStyle: React.CSSProperties = {
    background: currentBackground.value,
    minHeight: '100vh',
    transition: 'background 0.3s ease-in-out',
  }

  // Determine if background is dark
  const isDarkBackground = currentBackground.textColor === 'light'

  // Check if current combination is accessible
  const isAccessible = meetsWCAGAA(currentBackground.dominantColor, textColor)

  /**
   * Set a new background by ID
   */
  const setBackground = useCallback((backgroundId: string) => {
    const background = BOARD_BACKGROUNDS.find(bg => bg.id === backgroundId)
    if (background) {
      setCurrentBackground(background)
    }
  }, [])

  /**
   * Get background by ID
   */
  const getBackgroundById = useCallback((id: string): BoardBackground | undefined => {
    return BOARD_BACKGROUNDS.find(bg => bg.id === id)
  }, [])

  /**
   * Get all available backgrounds
   */
  const getAllBackgrounds = useCallback((): BoardBackground[] => {
    return BOARD_BACKGROUNDS
  }, [])

  /**
   * Apply dynamic theme based on background
   */
  const applyDynamicTheme = useCallback((background: BoardBackground) => {
    // Set CSS custom properties for dynamic theming
    const root = document.documentElement
    
    // Set dynamic background color
    root.style.setProperty('--dynamic-background-color', background.dominantColor)
    
    // Set dynamic text color based on background brightness
    const dynamicTextColor = getTextColorForBackground(background.dominantColor)
    root.style.setProperty('--dynamic-text-color', dynamicTextColor)
    
    // Set transparency variants
    const backgroundWithAlpha = background.dominantColor + '90' // 90% opacity
    root.style.setProperty('--dynamic-background-color-transparent', backgroundWithAlpha)
    
    const textWithAlpha = dynamicTextColor + '80' // 50% opacity
    root.style.setProperty('--dynamic-text-color-transparent', textWithAlpha)
    
    // Set icon color (same as text color)
    root.style.setProperty('--dynamic-icon-color', dynamicTextColor)
    
    // Update local state
    setTextColor(dynamicTextColor)
  }, [])

  /**
   * Reset theme to default
   */
  const resetTheme = useCallback(() => {
    const root = document.documentElement
    
    // Remove custom properties
    root.style.removeProperty('--dynamic-background-color')
    root.style.removeProperty('--dynamic-text-color')
    root.style.removeProperty('--dynamic-background-color-transparent')
    root.style.removeProperty('--dynamic-text-color-transparent')
    root.style.removeProperty('--dynamic-icon-color')
    
    // Reset to default
    setTextColor('#ffffff')
  }, [])

  // Update theme when background changes
  useEffect(() => {
    applyDynamicTheme(currentBackground)
    
    // Calculate contrast ratio
    const ratio = getContrastRatio(currentBackground.dominantColor, textColor)
    setContrastRatio(ratio)
    
    return () => {
      // Cleanup on unmount
      resetTheme()
    }
  }, [currentBackground, textColor, applyDynamicTheme, resetTheme])

  return {
    // Current background state
    currentBackground,
    backgroundStyle,
    textColor,
    isDarkBackground,
    
    // Background management
    setBackground,
    getBackgroundById,
    getAllBackgrounds,
    
    // Dynamic theming
    applyDynamicTheme,
    resetTheme,
    
    // Accessibility
    contrastRatio,
    isAccessible,
  }
}

export default useBoardBackground
